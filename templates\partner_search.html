{% extends "base.html" %}

{% block title %}学员信息查询{% endblock %}

{% block content %}
<div class="container">
    <div class="search-section">
        <div class="search-header">
            <h2>学员信息查询</h2>
            <p class="search-description">
                输入学员序号，查询其联系信息和连队分配情况
            </p>
        </div>

        <div class="search-form-container">
            <form method="POST" class="search-form" id="searchForm">
                <div class="form-group">
                    <input type="number"
                           id="sequence_number"
                           name="sequence_number"
                           placeholder="请输入序号（1-1333）"
                           min="1"
                           max="1333"
                           required
                           class="form-input">
                </div>

                {% if error %}
                <div class="error-message">
                    {{ error }}
                </div>
                {% endif %}

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-search">
                        查询学员信息
                    </button>
                </div>
            </form>
        </div>

        <div class="usage-info" id="usageInfo">
            <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
            </div>
        </div>


    </div>
</div>

<script>
// 手机号格式化
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, ''); // 只保留数字
    if (value.length > 11) {
        value = value.slice(0, 11);
    }
    e.target.value = value;
});

// 表单验证
document.getElementById('searchForm').addEventListener('submit', function(e) {
    const sequenceNumber = document.getElementById('sequence_number').value;

    if (!sequenceNumber) {
        e.preventDefault();
        alert('请输入序号');
        return;
    }

    const num = parseInt(sequenceNumber);
    if (isNaN(num) || num < 1 || num > 1333) {
        e.preventDefault();
        alert('请输入有效的序号（1-1333）');
        return;
    }

    // 显示加载状态
    const submitBtn = document.querySelector('.btn-search');
    submitBtn.innerHTML = '查询中...';
    submitBtn.disabled = true;

    // 提交后延迟更新访问状态
    setTimeout(() => {
        updateAccessStatus();
    }, 2000);
});

// 实时更新访问状态
function updateAccessStatus() {
    fetch('/api/access-status')
        .then(response => response.json())
        .then(data => {
            const remainingCount = document.getElementById('remainingCount');
            const progressFill = document.getElementById('progressFill');

            if (remainingCount && progressFill) {
                remainingCount.textContent = `剩余查询次数：${data.access_info.remaining_visits}/${data.access_info.max_visits}`;

                const percentage = (data.access_info.current_count / data.access_info.max_visits * 100);
                progressFill.style.width = percentage + '%';

                // 如果次数用完，禁用提交按钮
                const submitBtn = document.querySelector('.btn-search');
                if (!data.is_allowed) {
                    submitBtn.disabled = true;
                    submitBtn.textContent = '查询次数已用完';
                    submitBtn.classList.add('btn-disabled');
                } else {
                    submitBtn.disabled = false;
                    submitBtn.textContent = '查询学员信息';
                    submitBtn.classList.remove('btn-disabled');
                }
            }
        })
        .catch(error => {
            console.error('更新访问状态失败:', error);
        });
}

// 页面加载时聚焦到输入框并更新访问状态
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('sequence_number').focus();
    updateAccessStatus();

    // 每30秒更新一次访问状态
    setInterval(updateAccessStatus, 30000);
});
</script>

<style>
.search-section {
    max-width: 400px;
    margin: 0 auto;
    padding: 2rem;
}

.search-header {
    text-align: center;
    margin-bottom: 3rem;
}

.search-header h2 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.search-description {
    color: #666;
    font-size: 1rem;
}

.search-form-container {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 2rem;
}

.form-input {
    width: 100%;
    padding: 1.2rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1.2rem;
    text-align: center;
    transition: border-color 0.3s;
}

.form-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.error-message {
    background: #fee;
    border: 1px solid #e74c3c;
    color: #e74c3c;
    padding: 1rem;
    border-radius: 5px;
    margin-top: 1rem;
    text-align: center;
}

.form-actions {
    text-align: center;
}

.btn-search {
    background: #3498db;
    border: none;
    padding: 1rem 3rem;
    font-size: 1.1rem;
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-search:hover {
    background: #2980b9;
}

.btn-disabled {
    background: #95a5a6 !important;
    cursor: not-allowed !important;
}

.btn-disabled:hover {
    background: #95a5a6 !important;
}

.usage-info {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .search-section {
        padding: 1rem;
    }

    .form-input {
        font-size: 1rem;
    }
}
</style>
{% endblock %}
