"""
测试新的main.py应用
验证所有功能是否正常工作
"""

import requests
import time
import json

def test_main_app():
    """测试主应用的所有功能"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 测试新的main.py应用")
    print("=" * 50)
    
    # 测试1: 健康检查
    print("1. 测试健康检查...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 健康检查通过: {data['status']}")
            print(f"   📊 静态文件可用: {data['static_files_available']}")
        else:
            print(f"   ❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 健康检查异常: {e}")
    
    # 测试2: 首页访问
    print("\n2. 测试首页访问...")
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("   ✅ 首页访问成功")
        else:
            print(f"   ❌ 首页访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 首页访问异常: {e}")
    
    # 测试3: 搜索页面
    print("\n3. 测试搜索页面...")
    try:
        response = requests.get(f"{base_url}/search", timeout=5)
        if response.status_code == 200:
            print("   ✅ 搜索页面访问成功")
        else:
            print(f"   ❌ 搜索页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 搜索页面访问异常: {e}")
    
    # 测试4: API查询
    print("\n4. 测试API查询...")
    try:
        response = requests.post(f"{base_url}/api/search", 
                               json={'sequence_number': '1'}, 
                               timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ API查询成功")
                print(f"   📝 学员信息: {data['student_info']['wechat_nickname']}")
                print(f"   🔗 静态文件URL: {data.get('static_url')}")
            else:
                print(f"   ❌ API查询失败: {data.get('error')}")
        else:
            print(f"   ❌ API查询失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API查询异常: {e}")
    
    # 测试5: 静态文件直接访问
    print("\n5. 测试静态文件直接访问...")
    try:
        start_time = time.time()
        response = requests.get(f"{base_url}/static/query/1.json", timeout=5)
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            duration = (end_time - start_time) * 1000
            print(f"   ✅ 静态文件访问成功")
            print(f"   ⚡ 响应时间: {duration:.2f}ms")
            print(f"   📝 学员信息: {data['student_info']['wechat_nickname']}")
        else:
            print(f"   ❌ 静态文件访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 静态文件访问异常: {e}")
    
    # 测试6: 系统统计
    print("\n6. 测试系统统计...")
    try:
        response = requests.get(f"{base_url}/api/stats", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 系统统计获取成功")
            print(f"   👥 总学员数: {data.get('total_students')}")
            print(f"   🚀 性能模式: {data.get('performance_mode')}")
        else:
            print(f"   ❌ 系统统计失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 系统统计异常: {e}")
    
    # 测试7: 错误处理
    print("\n7. 测试错误处理...")
    try:
        response = requests.post(f"{base_url}/api/search", 
                               json={'sequence_number': '9999'}, 
                               timeout=5)
        if response.status_code == 400:
            data = response.json()
            print("   ✅ 错误处理正常")
            print(f"   📝 错误信息: {data.get('error')}")
        else:
            print(f"   ⚠️ 错误处理异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 错误处理测试异常: {e}")
    
    # 测试8: 性能测试
    print("\n8. 简单性能测试...")
    try:
        times = []
        for i in range(5):
            start_time = time.time()
            response = requests.get(f"{base_url}/static/query/{i+1}.json", timeout=5)
            end_time = time.time()
            
            if response.status_code == 200:
                times.append((end_time - start_time) * 1000)
        
        if times:
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"   ✅ 性能测试完成")
            print(f"   ⚡ 平均响应时间: {avg_time:.2f}ms")
            print(f"   🚀 最快响应时间: {min_time:.2f}ms")
            print(f"   🐌 最慢响应时间: {max_time:.2f}ms")
            
            if avg_time < 50:
                print("   🎉 性能优秀！")
            elif avg_time < 100:
                print("   👍 性能良好")
            else:
                print("   ⚠️ 性能需要优化")
        else:
            print("   ❌ 性能测试失败")
    except Exception as e:
        print(f"   ❌ 性能测试异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    print("\n💡 使用建议:")
    print("- 直接访问静态文件获得最佳性能")
    print("- 使用API接口保持访问控制")
    print("- 监控响应时间确保性能")

if __name__ == "__main__":
    test_main_app()
