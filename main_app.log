2025-08-13 17:40:13,876 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:40:13,876 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:40:49,312 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:49] "[31m[1mGET /api/search HTTP/1.1[0m" 405 -
2025-08-13 17:40:49,470 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:49] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-13 17:40:52,853 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:52] "[33mGET /static/query/{序号}.json HTTP/1.1[0m" 404 -
2025-08-13 17:40:52,906 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:52] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:40:56,471 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:56] "GET / HTTP/1.1" 200 -
2025-08-13 17:40:56,484 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:56] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:00,385 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:42:00,385 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:42:06,112 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:06] "GET / HTTP/1.1" 200 -
2025-08-13 17:42:06,272 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:06] "GET /static/style.css HTTP/1.1" 200 -
2025-08-13 17:42:06,392 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:06] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-13 17:42:08,016 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:08,024 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:08] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:08,049 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:08] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:10,349 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:10,353 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:10] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:10,363 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:10] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:11,133 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:11,136 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:11] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:11,146 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:11] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:11,987 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:11,989 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:11] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,000 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,277 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,280 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,294 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,444 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,448 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,458 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,588 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,592 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,602 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,724 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,729 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,740 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,851 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,855 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,865 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,985 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,988 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:13,001 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:13] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:13,222 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:13,226 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:13] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:13,236 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:13] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:25,312 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:42:25,312 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:42:30,240 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:30] "GET / HTTP/1.1" 200 -
2025-08-13 17:42:30,406 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:30] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:30,473 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:30] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-13 17:42:31,722 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:31,727 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:31] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:31,743 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:31] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:44:04,409 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:44:04,409 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:45:25,161 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /health HTTP/1.1" 200 -
2025-08-13 17:45:25,168 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET / HTTP/1.1" 200 -
2025-08-13 17:45:25,172 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    # 提供默认的access_info以兼容模板
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:45:25,180 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:45:25,202 - INFO - 静态文件查询耗时: 1.37ms
2025-08-13 17:45:25,203 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "POST /api/search HTTP/1.1" 200 -
2025-08-13 17:45:25,210 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /static/query/1.json HTTP/1.1" 200 -
2025-08-13 17:45:25,214 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /api/stats HTTP/1.1" 200 -
2025-08-13 17:45:25,218 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "[31m[1mPOST /api/search HTTP/1.1[0m" 400 -
2025-08-13 17:45:25,224 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /static/query/1.json HTTP/1.1" 200 -
2025-08-13 17:45:25,228 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /static/query/2.json HTTP/1.1" 200 -
2025-08-13 17:45:25,232 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "[33mGET /static/query/3.json HTTP/1.1[0m" 404 -
2025-08-13 17:45:25,239 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /static/query/4.json HTTP/1.1" 200 -
2025-08-13 17:45:25,265 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "[33mGET /static/query/5.json HTTP/1.1[0m" 404 -
2025-08-13 17:46:25,973 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:46:25,973 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:46:48,947 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    # 提供默认的access_info以兼容模板
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:46:48,959 - INFO - 127.0.0.1 - - [13/Aug/2025 17:46:48] "[35m[1mGET /search HTTP/1.1[0m" 500 -
