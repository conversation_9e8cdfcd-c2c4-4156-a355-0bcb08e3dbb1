2025-08-13 17:40:13,876 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:40:13,876 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:40:49,312 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:49] "[31m[1mGET /api/search HTTP/1.1[0m" 405 -
2025-08-13 17:40:49,470 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:49] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-13 17:40:52,853 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:52] "[33mGET /static/query/{序号}.json HTTP/1.1[0m" 404 -
2025-08-13 17:40:52,906 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:52] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:40:56,471 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:56] "GET / HTTP/1.1" 200 -
2025-08-13 17:40:56,484 - INFO - 127.0.0.1 - - [13/Aug/2025 17:40:56] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:00,385 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:42:00,385 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:42:06,112 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:06] "GET / HTTP/1.1" 200 -
2025-08-13 17:42:06,272 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:06] "GET /static/style.css HTTP/1.1" 200 -
2025-08-13 17:42:06,392 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:06] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-13 17:42:08,016 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:08,024 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:08] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:08,049 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:08] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:10,349 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:10,353 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:10] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:10,363 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:10] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:11,133 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:11,136 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:11] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:11,146 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:11] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:11,987 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:11,989 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:11] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,000 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,277 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,280 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,294 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,444 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,448 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,458 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,588 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,592 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,602 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,724 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,729 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,740 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,851 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,855 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:12,865 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:12,985 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:12,988 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:12] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:13,001 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:13] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:13,222 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:13,226 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:13] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:13,236 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:13] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:25,312 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:42:25,312 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:42:30,240 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:30] "GET / HTTP/1.1" 200 -
2025-08-13 17:42:30,406 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:30] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:42:30,473 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:30] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-08-13 17:42:31,722 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    return render_template('partner_search.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:42:31,727 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:31] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:42:31,743 - INFO - 127.0.0.1 - - [13/Aug/2025 17:42:31] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:44:04,409 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:44:04,409 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:45:25,161 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /health HTTP/1.1" 200 -
2025-08-13 17:45:25,168 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET / HTTP/1.1" 200 -
2025-08-13 17:45:25,172 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    # 提供默认的access_info以兼容模板
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:45:25,180 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:45:25,202 - INFO - 静态文件查询耗时: 1.37ms
2025-08-13 17:45:25,203 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "POST /api/search HTTP/1.1" 200 -
2025-08-13 17:45:25,210 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /static/query/1.json HTTP/1.1" 200 -
2025-08-13 17:45:25,214 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /api/stats HTTP/1.1" 200 -
2025-08-13 17:45:25,218 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "[31m[1mPOST /api/search HTTP/1.1[0m" 400 -
2025-08-13 17:45:25,224 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /static/query/1.json HTTP/1.1" 200 -
2025-08-13 17:45:25,228 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /static/query/2.json HTTP/1.1" 200 -
2025-08-13 17:45:25,232 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "[33mGET /static/query/3.json HTTP/1.1[0m" 404 -
2025-08-13 17:45:25,239 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "GET /static/query/4.json HTTP/1.1" 200 -
2025-08-13 17:45:25,265 - INFO - 127.0.0.1 - - [13/Aug/2025 17:45:25] "[33mGET /static/query/5.json HTTP/1.1[0m" 404 -
2025-08-13 17:46:25,973 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:46:25,973 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:46:48,947 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 111, in search_student
    # 提供默认的access_info以兼容模板
            ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 43, in block 'content'
    <p id="remainingCount">剩余查询次数：{{ access_info.remaining_visits }}/{{ access_info.max_visits }}</p>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 490, in getattr
    return getattr(obj, attribute)
           ^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'access_info' is undefined
2025-08-13 17:46:48,959 - INFO - 127.0.0.1 - - [13/Aug/2025 17:46:48] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:49:01,546 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:49:01,546 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:49:03,992 - INFO - 127.0.0.1 - - [13/Aug/2025 17:49:03] "GET / HTTP/1.1" 200 -
2025-08-13 17:49:04,170 - INFO - 127.0.0.1 - - [13/Aug/2025 17:49:04] "[36mGET /static/style.css HTTP/1.1[0m" 304 -
2025-08-13 17:49:04,300 - ERROR - Exception on /favicon.ico [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    return render_template('partner_search.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:49:04,307 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:49:04,309 - INFO - 127.0.0.1 - - [13/Aug/2025 17:49:04] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-08-13 17:49:04,339 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    return render_template('partner_search.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    return render_template('partner_search.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:49:05,963 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 120, in search_student
    return render_template('partner_search.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:49:05,966 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:49:05,967 - INFO - 127.0.0.1 - - [13/Aug/2025 17:49:05] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:49:05,971 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 120, in search_student
    return render_template('partner_search.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    return render_template('partner_search.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:49:06,009 - ERROR - Exception on /favicon.ico [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    return render_template('partner_search.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:49:06,011 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:49:06,012 - INFO - 127.0.0.1 - - [13/Aug/2025 17:49:06] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-08-13 17:49:06,016 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    return render_template('partner_search.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    return render_template('partner_search.html',
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:04,777 - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://**************:5000
2025-08-13 17:55:04,779 - INFO - [33mPress CTRL+C to quit[0m
2025-08-13 17:55:16,450 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 120, in search_student
    if request.method == 'GET':
               ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:16,453 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:55:16,454 - INFO - 127.0.0.1 - - [13/Aug/2025 17:55:16] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:55:16,458 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 120, in search_student
    if request.method == 'GET':
               ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    except FileNotFoundError:
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:16,505 - ERROR - Exception on /favicon.ico [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:16,509 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:55:16,510 - INFO - 127.0.0.1 - - [13/Aug/2025 17:55:16] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-08-13 17:55:16,516 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    except FileNotFoundError:
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:19,202 - ERROR - Exception on /search [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 120, in search_student
    if request.method == 'GET':
               ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:19,205 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:55:19,206 - INFO - 127.0.0.1 - - [13/Aug/2025 17:55:19] "[35m[1mGET /search HTTP/1.1[0m" 500 -
2025-08-13 17:55:19,211 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 120, in search_student
    if request.method == 'GET':
               ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    except FileNotFoundError:
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:19,246 - ERROR - Exception on /favicon.ico [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:19,251 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:55:19,252 - INFO - 127.0.0.1 - - [13/Aug/2025 17:55:19] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-08-13 17:55:19,257 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    except FileNotFoundError:
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:27,147 - ERROR - Exception on /nonexistent [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:27,151 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:55:27,152 - INFO - 127.0.0.1 - - [13/Aug/2025 17:55:27] "[35m[1mGET /nonexistent HTTP/1.1[0m" 500 -
2025-08-13 17:55:27,157 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    except FileNotFoundError:
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:27,309 - ERROR - Exception on /favicon.ico [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:27,313 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:55:27,314 - INFO - 127.0.0.1 - - [13/Aug/2025 17:55:27] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-08-13 17:55:27,318 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    except FileNotFoundError:
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:37,256 - INFO - 127.0.0.1 - - [13/Aug/2025 17:55:37] "[33mGET /api/access-status HTTP/1.1[0m" 404 -
2025-08-13 17:55:37,312 - ERROR - Exception on /favicon.ico [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:55:37,314 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:55:37,315 - INFO - 127.0.0.1 - - [13/Aug/2025 17:55:37] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-08-13 17:55:37,319 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    except FileNotFoundError:
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:56:05,861 - INFO - 127.0.0.1 - - [13/Aug/2025 17:56:05] "[33mGET /api/access-status HTTP/1.1[0m" 404 -
2025-08-13 17:56:05,899 - ERROR - Exception on /favicon.ico [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:56:05,903 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:56:05,903 - INFO - 127.0.0.1 - - [13/Aug/2025 17:56:05] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-08-13 17:56:05,911 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    except FileNotFoundError:
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:56:06,629 - ERROR - Exception on /nonexistent [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:56:06,634 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:56:06,635 - INFO - 127.0.0.1 - - [13/Aug/2025 17:56:06] "[35m[1mGET /nonexistent HTTP/1.1[0m" 500 -
2025-08-13 17:56:06,641 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    except FileNotFoundError:
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:56:06,681 - ERROR - Exception on /favicon.ico [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
2025-08-13 17:56:06,685 - ERROR - 服务器内部错误: 500 Internal Server Error: The server encountered an internal error and was unable to complete your request. Either the server is overloaded or there is an error in the application.
2025-08-13 17:56:06,686 - INFO - 127.0.0.1 - - [13/Aug/2025 17:56:06] "[35m[1mGET /favicon.ico HTTP/1.1[0m" 500 -
2025-08-13 17:56:06,693 - ERROR - Error on request:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 891, in dispatch_request
    self.raise_routing_exception(req)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 500, in raise_routing_exception
    raise request.routing_exception  # type: ignore[misc]
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\ctx.py", line 362, in match_request
    result = self.url_adapter.match(return_rule=True)  # type: ignore
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\routing\map.py", line 629, in match
    raise NotFound() from None
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 802, in handle_user_exception
    return self.handle_http_exception(e)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 777, in handle_http_exception
    return self.ensure_sync(handler)(e)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 229, in not_found
    'status': 'healthy',
       ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 370, in run_wsgi
    execute(self.server.app)
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\werkzeug\serving.py", line 331, in execute
    application_iter = app(environ, start_response)
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1536, in __call__
    return self.wsgi_app(environ, start_response)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 1514, in wsgi_app
    response = self.handle_exception(e)
               ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\app.py", line 860, in handle_exception
    server_error = self.ensure_sync(handler)(server_error)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Desktop\web访问限制\main.py", line 242, in internal_error
    except FileNotFoundError:
           ^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
         ^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
  File "C:\Users\<USER>\Desktop\web访问限制\.venv\Lib\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\base.html", line 23, in top-level template code
    {% block content %}{% endblock %}
  File "c:\Users\<USER>\Desktop\web访问限制\templates\partner_search.html", line 45, in block 'content'
    <div class="progress-fill" id="progressFill" style="width: {{ (access_info.current_count / access_info.max_visits * 100) if access_info.current_count is defined else 0 }}%"></div>
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'current_count'
