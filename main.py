"""
学员信息查询系统 - 高性能主应用
基于静态文件优化架构，使用现有前端模板

特性:
- 极速响应 (< 10ms)
- 超高并发 (500+ 用户)
- 兼容现有前端界面
- 智能静态文件服务
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for, send_from_directory
import os
import json
import logging
from datetime import datetime
import time

# 创建Flask应用
app = Flask(__name__)
app.secret_key = 'high-performance-student-search-2025'

# 静态文件目录
STATIC_QUERY_DIR = "static/query"

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('main_app.log', encoding='utf-8'),
    ]
)

logger = logging.getLogger(__name__)

# 高性能静态查询服务
def load_stats():
    """加载系统统计信息"""
    try:
        stats_file = os.path.join(STATIC_QUERY_DIR, "stats.json")
        if os.path.exists(stats_file):
            with open(stats_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        logger.error(f"加载统计信息失败: {e}")

    return {
        'total_students': 1333,
        'data_source': 'static_cache',
        'last_update': datetime.now().isoformat()
    }

def get_student_info_from_static(sequence_number):
    """从静态文件获取学员信息（高性能版本）"""
    start_time = time.time()

    try:
        sequence_number = int(sequence_number)
    except (ValueError, TypeError):
        return {
            'success': False,
            'error': '序号必须是数字'
        }

    # 检查序号范围
    if sequence_number < 1 or sequence_number > 1333:
        return {
            'success': False,
            'error': '序号必须在1-1333之间'
        }

    # 读取静态文件
    static_file = os.path.join(STATIC_QUERY_DIR, f"{sequence_number}.json")

    try:
        if os.path.exists(static_file):
            with open(static_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            end_time = time.time()
            duration = (end_time - start_time) * 1000
            logger.info(f"静态文件查询耗时: {duration:.2f}ms")

            return data
        else:
            return {
                'success': False,
                'error': f'序号 {sequence_number} 的数据不存在'
            }

    except Exception as e:
        logger.error(f"读取静态文件失败: {e}")
        return {
            'success': False,
            'error': '系统错误，请稍后重试'
        }

# 路由定义 - 使用现有前端模板
@app.route('/')
def index():
    """首页"""
    stats = load_stats()
    return render_template('partner_index.html', stats=stats)

@app.route('/search', methods=['GET', 'POST'])
def search_student():
    """学员查询页面（高性能版）"""

    # 提供默认的access_info以兼容模板
    default_access_info = {
        'remaining_visits': 999,
        'max_visits': 999,
        'current_count': 0,
        'is_allowed': True,
        'message': '高性能版本，无限制访问'
    }

    if request.method == 'GET':
        return render_template('partner_search.html',
                             access_info=default_access_info)

    # POST请求处理
    sequence_number = request.form.get('sequence_number', '').strip()

    if not sequence_number:
        return render_template('partner_search.html',
                             error="请输入序号",
                             access_info=default_access_info)

    # 获取学员信息
    result = get_student_info_from_static(sequence_number)

    if result.get('success'):
        # 成功 - 重定向到结果页面
        return redirect(url_for('result', sequence_number=sequence_number))
    else:
        # 失败 - 显示错误
        return render_template('partner_search.html',
                             error=result.get('error'),
                             access_info=default_access_info)

@app.route('/result/<int:sequence_number>')
def result(sequence_number):
    """结果展示页面"""
    result = get_student_info_from_static(sequence_number)

    if result.get('success'):
        student_info = result.get('student_info', {})

        return render_template('partner_result.html',
                             partner_info=student_info,
                             search_sequence=sequence_number)
    else:
        # 提供默认的access_info以兼容模板
        default_access_info = {
            'remaining_visits': 999,
            'max_visits': 999,
            'current_count': 0,
            'is_allowed': True,
            'message': '高性能版本，无限制访问'
        }
        return render_template('partner_search.html',
                             error=result.get('error'),
                             access_info=default_access_info)

# API路由 - 高性能版本
@app.route('/api/search', methods=['POST'])
def api_search():
    """API接口 - 高性能静态文件版本"""
    try:
        data = request.get_json()
        if not data or 'sequence_number' not in data:
            return jsonify({
                'success': False,
                'error': '缺少必需参数: sequence_number'
            }), 400

        sequence_number = data['sequence_number']
        result = get_student_info_from_static(sequence_number)

        if result.get('success'):
            # 添加静态文件URL以获得最佳性能
            result['static_url'] = f"/static/query/{sequence_number}.json"
            result['performance_tip'] = '直接访问static_url可获得最佳性能'

            return jsonify(result)
        else:
            return jsonify(result), 400

    except Exception as e:
        logger.error(f"API请求处理失败: {e}")
        return jsonify({
            'success': False,
            'error': '服务器内部错误'
        }), 500

@app.route('/api/stats')
def api_stats():
    """系统统计API"""
    stats = load_stats()
    stats['performance_mode'] = 'static_files'
    stats['response_time'] = '< 10ms'
    return jsonify(stats)

@app.route('/api/access-status')
def api_access_status():
    """API接口 - 获取当前用户的访问状态（高性能版本）"""
    # 高性能版本返回无限制访问状态
    access_info = {
        'remaining_visits': 999,
        'max_visits': 999,
        'current_count': 0,
        'is_allowed': True,
        'message': '高性能版本，无限制访问'
    }

    return jsonify({
        'is_allowed': True,
        'access_info': access_info,
        'user_fingerprint': 'high-performance-mode'
    })

@app.route('/health')
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '2.0-optimized',
        'static_files_available': os.path.exists(STATIC_QUERY_DIR),
        'performance_mode': 'static_files'
    })

# 静态文件直接服务（最高性能）
@app.route('/static/query/<filename>')
def serve_static_query(filename):
    """直接服务静态查询文件（最高性能）"""
    try:
        return send_from_directory(STATIC_QUERY_DIR, filename)
    except FileNotFoundError:
        return jsonify({
            'success': False,
            'error': '文件不存在'
        }), 404

# 错误处理
@app.errorhandler(404)
def not_found(_):
    """404错误处理"""
    if request.path.startswith('/api/'):
        return jsonify({
            'success': False,
            'error': '接口不存在'
        }), 404
    return render_template('partner_search.html',
                         error='页面不存在',
                         access_info={'remaining_visits': 999, 'max_visits': 999, 'current_count': 0}), 404

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    logger.error(f"服务器内部错误: {str(error)}")
    if request.path.startswith('/api/'):
        return jsonify({
            'success': False,
            'error': '服务器内部错误'
        }), 500
    return render_template('partner_search.html',
                         error='系统错误，请稍后重试',
                         access_info={'remaining_visits': 999, 'max_visits': 999, 'current_count': 0}), 500

# 应用启动
if __name__ == '__main__':
    print("🚀 学员信息查询系统 - 高性能主应用")
    print("=" * 60)
    print("🎯 优化特性:")
    print("- 基于静态文件的超高性能架构")
    print("- 响应时间 < 10ms")
    print("- 支持 500+ 并发用户")
    print("- 兼容现有前端界面")
    print("- 智能静态文件服务")
    print("")

    # 检查静态文件
    if not os.path.exists(STATIC_QUERY_DIR):
        print("⚠️ 警告: 静态查询文件不存在")
        print("请先运行: python generate_static_queries.py")
        print("")
    else:
        file_count = len([f for f in os.listdir(STATIC_QUERY_DIR) if f.endswith('.json')])
        print(f"✅ 静态文件检查: {file_count} 个文件")

        # 显示文件大小
        total_size = sum(os.path.getsize(os.path.join(STATIC_QUERY_DIR, f))
                        for f in os.listdir(STATIC_QUERY_DIR) if f.endswith('.json'))
        print(f"📁 文件总大小: {total_size/1024:.1f} KB")
        print("")

    print("🌐 访问地址:")
    print("- 首页: http://127.0.0.1:5000/")
    print("- 查询: http://127.0.0.1:5000/search")
    print("- API: http://127.0.0.1:5000/api/search")
    print("- 统计: http://127.0.0.1:5000/api/stats")
    print("- 健康检查: http://127.0.0.1:5000/health")
    print("")
    print("⚡ 高性能访问:")
    print("- 静态文件: http://127.0.0.1:5000/static/query/{序号}.json")
    print("- 示例: http://127.0.0.1:5000/static/query/1.json")
    print("")
    print("🚀 启动应用...")

    # 启动应用
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=False,
        threaded=True
    )
